"""
Forms for attendance record management.
"""

from flask_wtf import <PERSON>laskForm
from wtforms import StringField, DateField, TextAreaField, SelectField, SubmitField, TimeField, DecimalField, HiddenField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, ValidationError, NumberRange
from datetime import date, time
from flask_login import current_user

from app import db
from app.models.attendance import AttendanceRecord, AttendanceType
from app.models.employee import EmployeeDetail
from app.services.holiday_service import HolidayService


class AttendanceRecordForm(FlaskForm):
    """Form for creating and editing attendance records."""

    employee_detail_id = SelectField(
        'Employee',
        coerce=int,
        validators=[DataRequired(message="Employee is required.")],
        render_kw={"placeholder": "Select an employee"}
    )

    date = DateField(
        'Date',
        validators=[DataRequired(message="Date is required.")],
        format='%Y-%m-%d'
    )

    attendance_type_id = SelectField(
        'Attendance Type',
        coerce=int,
        validators=[DataRequired(message="Attendance type is required.")],
        render_kw={"placeholder": "Select attendance type"}
    )

    start_time = TimeField(
        'Start Time',
        validators=[Optional()],
        description="Required for partial day attendance types"
    )

    end_time = TimeField(
        'End Time',
        validators=[Optional()],
        description="Required for partial day attendance types"
    )

    duration_hours = DecimalField(
        'Duration (Hours)',
        validators=[
            Optional(),
            NumberRange(min=0.5, max=24, message="Duration must be between 0.5 and 24 hours")
        ],
        places=2,
        description="For partial day types (e.g., 4.00 for half day)"
    )

    notes = TextAreaField(
        'Notes',
        validators=[Optional(), Length(max=500, message="Notes cannot exceed 500 characters.")],
        render_kw={"rows": 3, "placeholder": "Optional notes or remarks"}
    )

    status = SelectField(
        'Status',
        choices=AttendanceRecord.STATUS_CHOICES,
        validators=[DataRequired(message="Status is required.")],
        default=AttendanceRecord.STATUS_PENDING
    )

    # Holiday work fields
    is_holiday_work = BooleanField(
        'Holiday Work',
        description="Check if this is work performed on a holiday"
    )

    holiday_work_reason = TextAreaField(
        'Holiday Work Reason',
        validators=[Optional(), Length(max=500, message="Holiday work reason cannot exceed 500 characters.")],
        render_kw={"rows": 3, "placeholder": "Please provide a reason for working on this holiday"}
    )

    # Hidden fields for admin use
    approved_by_id = HiddenField()
    rejection_reason = TextAreaField(
        'Rejection Reason',
        validators=[Optional(), Length(max=500)],
        render_kw={"rows": 2, "placeholder": "Reason for rejection (admin only)"}
    )

    submit = SubmitField('Save Attendance Record')

    # Store original values for edit validation
    original_date = None
    original_employee_id = None
    original_record_id = None

    def __init__(self, original_date=None, original_employee_id=None, original_record_id=None, *args, **kwargs):
        super(AttendanceRecordForm, self).__init__(*args, **kwargs)

        # Store original values
        self.original_date = original_date
        self.original_employee_id = original_employee_id
        self.original_record_id = original_record_id

        # Populate employee choices
        self._populate_employee_choices()

        # Populate attendance type choices
        self._populate_attendance_type_choices()

    def _populate_employee_choices(self):
        """Populate employee choices based on user role."""
        try:
            if hasattr(current_user, 'is_admin') and current_user.is_admin:
                # Admin can see all active employees
                employees = EmployeeDetail.query.filter(
                    EmployeeDetail.emp_status == EmployeeDetail.STATUS_ACTIVE
                ).order_by(EmployeeDetail.first_name, EmployeeDetail.last_name).all()

                self.employee_detail_id.choices = [
                    (emp.id, f"{emp.first_name} {emp.last_name} ({emp.employee_number})")
                    for emp in employees
                ]
            elif hasattr(current_user, 'employee_detail') and current_user.employee_detail:
                # Regular users can only see their own record
                emp = current_user.employee_detail
                self.employee_detail_id.choices = [
                    (emp.id, f"{emp.first_name} {emp.last_name} ({emp.employee_number})")
                ]
                self.employee_detail_id.data = emp.id
            else:
                # Fallback: show all employees (for testing or when no user context)
                employees = EmployeeDetail.query.filter(
                    EmployeeDetail.emp_status == EmployeeDetail.STATUS_ACTIVE
                ).order_by(EmployeeDetail.first_name, EmployeeDetail.last_name).all()

                self.employee_detail_id.choices = [
                    (emp.id, f"{emp.first_name} {emp.last_name} ({emp.employee_number})")
                    for emp in employees
                ]
        except Exception as e:
            # Fallback for any errors (e.g., no user context)
            employees = EmployeeDetail.query.filter(
                EmployeeDetail.emp_status == EmployeeDetail.STATUS_ACTIVE
            ).order_by(EmployeeDetail.first_name, EmployeeDetail.last_name).all()

            self.employee_detail_id.choices = [
                (emp.id, f"{emp.first_name} {emp.last_name} ({emp.employee_number})")
                for emp in employees
            ]

    def _populate_attendance_type_choices(self):
        """Populate attendance type choices."""
        attendance_types = AttendanceType.query.order_by(AttendanceType.name).all()
        self.attendance_type_id.choices = [
            (at.id, f"{at.name} ({at.code})")
            for at in attendance_types
        ]

        # Store attendance type data for frontend use
        self._attendance_type_data = {
            at.id: {
                'is_full_day': at.is_full_day,
                'name': at.name,
                'code': at.code
            }
            for at in attendance_types
        }

    def validate_date(self, date_field):
        """Validate attendance date with holiday checking."""
        # Don't validate if date is empty (handled by DataRequired)
        if not date_field.data:
            return

        # Check for duplicate records (same employee, same date)
        if self._check_duplicate_record(date_field.data):
            raise ValidationError(
                f'An attendance record already exists for this employee on {date_field.data}. '
                'Please choose a different date or edit the existing record.'
            )

        # Check if date is a holiday and show warning
        holiday_info = self._check_holiday_date(date_field.data)
        if holiday_info['is_holiday']:
            # This is a warning, not an error - we'll handle this in the template
            self._holiday_warning = holiday_info
        else:
            self._holiday_warning = None

    def _check_duplicate_record(self, check_date):
        """Check if a record already exists for the same employee and date."""
        if not self.employee_detail_id.data:
            return False

        query = AttendanceRecord.query.filter(
            AttendanceRecord.employee_detail_id == self.employee_detail_id.data,
            AttendanceRecord.date == check_date
        )

        # Exclude current record when editing
        if self.original_record_id:
            query = query.filter(AttendanceRecord.id != self.original_record_id)

        return query.first() is not None

    def _check_holiday_date(self, check_date):
        """Check if the selected date is a holiday."""
        try:
            # Get employee region using enhanced detection
            if self.employee_detail_id.data:
                region_code = HolidayService.get_employee_region(self.employee_detail_id.data)
            else:
                region_code = 'PH'  # Default fallback

            is_holiday = HolidayService.is_holiday(check_date, region_code)

            if is_holiday:
                # Get holiday details
                from app.models.attendance import Holiday
                holiday = Holiday.query.filter(
                    Holiday.date == check_date,
                    db.or_(Holiday.region_code == region_code, Holiday.region_code == 'GLOBAL')
                ).first()

                return {
                    'is_holiday': True,
                    'holiday_name': holiday.name if holiday else 'Unknown Holiday',
                    'holiday_description': holiday.description if holiday else '',
                    'region_code': holiday.region_code if holiday else region_code,
                    'employee_region': region_code
                }

            return {
                'is_holiday': False,
                'employee_region': region_code
            }

        except Exception as e:
            # If holiday checking fails, don't block the form
            return {'is_holiday': False, 'error': str(e)}

    def validate_start_time(self, start_time_field):
        """Validate start time for partial day types."""
        if self.attendance_type_id.data:
            attendance_type = AttendanceType.query.get(self.attendance_type_id.data)
            if attendance_type and not attendance_type.is_full_day:
                if not start_time_field.data:
                    raise ValidationError('Start time is required for partial day attendance types.')

    def validate_end_time(self, end_time_field):
        """Validate end time for partial day types."""
        if self.attendance_type_id.data:
            attendance_type = AttendanceType.query.get(self.attendance_type_id.data)
            if attendance_type and not attendance_type.is_full_day:
                if not end_time_field.data:
                    raise ValidationError('End time is required for partial day attendance types.')

                # Validate end time is after start time
                if self.start_time.data and end_time_field.data:
                    if end_time_field.data <= self.start_time.data:
                        raise ValidationError('End time must be after start time.')

    def validate_duration_hours(self, duration_field):
        """Validate duration for partial day types."""
        if self.attendance_type_id.data:
            attendance_type = AttendanceType.query.get(self.attendance_type_id.data)
            if attendance_type and not attendance_type.is_full_day:
                if not duration_field.data:
                    raise ValidationError('Duration is required for partial day attendance types.')

    def validate_holiday_work_reason(self, reason_field):
        """Validate holiday work reason when holiday work is checked."""
        if self.is_holiday_work.data and not reason_field.data:
            raise ValidationError('Holiday work reason is required when marking as holiday work.')

        if self.is_holiday_work.data and reason_field.data and len(reason_field.data.strip()) < 10:
            raise ValidationError('Holiday work reason should be at least 10 characters long.')

    def validate_is_holiday_work(self, holiday_work_field):
        """Validate holiday work checkbox against actual holiday status."""
        if not self.date.data or not self.employee_detail_id.data:
            return

        # Check if the date is actually a holiday
        try:
            holiday_info = self._check_holiday_date(self.date.data)

            # Allow manual override: Only warn if holiday work is checked but date is not a holiday
            # Don't block the form submission - let users manually mark holiday work if needed
            if holiday_work_field.data and not holiday_info.get('is_holiday', False):
                # Store warning for display but don't raise validation error
                self._holiday_warning = {
                    'type': 'warning',
                    'message': f'Note: {self.date.data} is not recognized as a holiday, but you have marked this as holiday work.'
                }
                print(f"Holiday work warning: Date {self.date.data} is not a recognized holiday but marked as holiday work")
        except Exception as e:
            # If holiday checking fails, don't block the form submission
            # Log the error but allow the form to proceed
            print(f"Holiday validation error: {e}")
            pass

    def get_holiday_warning(self):
        """Get holiday warning information if available."""
        return getattr(self, '_holiday_warning', None)

    def auto_detect_holiday_work(self):
        """Auto-detect and set holiday work fields based on date."""
        if self.date.data and self.employee_detail_id.data:
            holiday_info = self._check_holiday_date(self.date.data)
            if holiday_info.get('is_holiday', False):
                self.is_holiday_work.data = True
                return holiday_info
        return None

    def get_attendance_type_data(self):
        """Get attendance type data for frontend use."""
        return getattr(self, '_attendance_type_data', {})


class AttendanceRecordFilterForm(FlaskForm):
    """Form for filtering attendance records."""

    employee = SelectField(
        'Employee',
        choices=[('', 'All Employees')],
        default=''
    )

    attendance_type = SelectField(
        'Attendance Type',
        choices=[('', 'All Types')],
        default=''
    )

    status = SelectField(
        'Status',
        choices=[('', 'All Statuses')] + AttendanceRecord.STATUS_CHOICES,
        default=''
    )

    start_date = DateField(
        'Start Date',
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    end_date = DateField(
        'End Date',
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    is_holiday_work = SelectField(
        'Holiday Work',
        choices=[('', 'All Records'), ('true', 'Holiday Work Only'), ('false', 'Regular Work Only')],
        default=''
    )

    submit = SubmitField('Filter')

    def __init__(self, *args, **kwargs):
        super(AttendanceRecordFilterForm, self).__init__(*args, **kwargs)
        self._populate_filter_choices()

    def _populate_filter_choices(self):
        """Populate filter choices."""
        # Employee choices
        employees = EmployeeDetail.query.filter(
            EmployeeDetail.emp_status == EmployeeDetail.STATUS_ACTIVE
        ).order_by(EmployeeDetail.first_name, EmployeeDetail.last_name).all()

        self.employee.choices = [('', 'All Employees')] + [
            (str(emp.id), f"{emp.first_name} {emp.last_name}")
            for emp in employees
        ]

        # Attendance type choices
        attendance_types = AttendanceType.query.order_by(AttendanceType.name).all()
        self.attendance_type.choices = [('', 'All Types')] + [
            (str(at.id), f"{at.name} ({at.code})")
            for at in attendance_types
        ]

    def validate_end_date(self, end_date_field):
        """Validate that end date is after start date."""
        if self.start_date.data and end_date_field.data:
            if end_date_field.data < self.start_date.data:
                raise ValidationError('End date must be after start date.')
